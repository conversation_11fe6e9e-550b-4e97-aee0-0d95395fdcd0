const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const Database = require('./database');
const dayjs = require('dayjs');

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

let mainWindow;
let db;
let currentUser = null; // Store current logged-in user

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    center: true, // Center the window on screen
    resizable: true,
    maximizable: true, // Enable maximize functionality
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    titleBarStyle: 'hidden', // Remove default toolbar
    frame: false, // Remove window frame for modern look
    show: false // Don't show window until ready (prevents console opening)
  });

  // Load the welcome page initially
  mainWindow.loadFile(path.join(__dirname, 'welcome.html'));

  // Show window when ready to prevent console opening
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Initialize database
  db = new Database();
  db.init();
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for login
ipcMain.handle('login', async (event, credentials) => {
  try {
    const user = await db.authenticateUser(credentials.username, credentials.password, credentials.role);
    if (user) {
      // Store current user information
      currentUser = user;

      // Load user permissions if not Admin
      let permissions = [];
      if (user.role !== 'Admin') {
        try {
          permissions = await db.getUserPermissions(user.id);
          console.log(`Loaded ${permissions.length} permissions for user ${user.username}`);
        } catch (permError) {
          console.error('Error loading user permissions:', permError);
        }
      }

      // Store permissions with user
      currentUser.permissions = permissions;

      // Automatically maximize window for application interfaces
      if (mainWindow && !mainWindow.isMaximized()) {
        mainWindow.maximize();
      }

      // Determine which interface to load based on user permissions
      const redirectPage = determineUserInterface(permissions, user.role);
      console.log(`Redirecting user ${user.username} (${user.role}) to: ${redirectPage}`);

      // Load the appropriate interface
      const pagePath = redirectPage === 'pos' ? 'pages/pos.html' :
                      redirectPage === 'admin' ? 'pages/admin.html' :
                      'pages/theater.html';

      mainWindow.loadFile(path.join(__dirname, pagePath));
      return { success: true, user: currentUser };
    } else {
      return { success: false, message: 'Invalid credentials' };
    }
  } catch (error) {
    return { success: false, message: 'Database error' };
  }
});

// Determine which interface to load based on user permissions
function determineUserInterface(permissions, userRole) {
  // Admin always goes to POS (can access everything)
  if (userRole === 'Admin') {
    return 'pos';
  }

  // For non-admin users, analyze their permissions
  if (!permissions || permissions.length === 0) {
    console.warn('User has no permissions, defaulting to POS');
    return 'pos';
  }

  const hasPos = permissions.some(p => p.module_id === 'pos');
  const hasTheater = permissions.some(p => p.module_id === 'theater');
  const hasAdmin = permissions.some(p =>
    p.module_id === 'dashboard' ||
    p.module_id === 'master' ||
    p.module_id === 'reports' ||
    p.module_id === 'transactions' ||
    p.module_id === 'wholesale' ||
    p.module_id === 'user-management'
  );

  console.log('Permission analysis:', { hasPos, hasTheater, hasAdmin });

  // Priority logic for redirection
  if (hasTheater && !hasPos && !hasAdmin) {
    // Only theater access - go directly to theater
    return 'theater';
  } else if (hasAdmin && !hasPos && !hasTheater) {
    // Only admin access - go directly to admin
    return 'admin';
  } else if (hasPos) {
    // Has POS access (with or without others) - go to POS
    return 'pos';
  } else {
    // Fallback to POS if unclear
    console.warn('Unclear permissions, defaulting to POS');
    return 'pos';
  }
}

// Get current user information
ipcMain.handle('get-current-user', () => {
  return currentUser;
});

// Location validation helper functions
function validateUserLocation() {
  if (!currentUser) {
    throw new Error('User not authenticated');
  }

  if (!currentUser.location_id) {
    throw new Error('User has no location assigned');
  }

  return true;
}

function isUserAdmin() {
  return currentUser && currentUser.role === 'Admin';
}

function canAccessAllLocations() {
  return isUserAdmin();
}

// Enhanced security logging
function logSecurityEvent(action, details = {}) {
  const timestamp = new Date().toISOString();
  const user = currentUser ? `${currentUser.username} (${currentUser.role})` : 'Unknown';
  const location = currentUser ? `${currentUser.location_name} (ID: ${currentUser.location_id})` : 'No location';

  console.log(`[SECURITY] ${timestamp} - ${action} - User: ${user} - Location: ${location}`, details);
}

// Logout handler
ipcMain.handle('logout', async () => {
  try {
    // Clear current user session
    currentUser = null;

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    // Reset window to original login state (fixed size, centered)
    if (mainWindow) {
      // Unmaximize if currently maximized
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      }

      // Reset to original dimensions and center
      mainWindow.setSize(1200, 800);
      mainWindow.center();
    }

    // Navigate to welcome page instead of login page
    await mainWindow.loadFile(path.join(__dirname, 'welcome.html'));

    // Wait for welcome page to load
    await new Promise(resolve => {
      mainWindow.webContents.once('did-finish-load', () => {
        resolve();
      });
    });

    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return { success: false, message: 'Logout failed' };
  }
});

// IPC handlers for navigation
ipcMain.handle('navigate-to', async (event, page) => {
  try {
    switch (page) {
      case 'pos':
        // Automatically maximize window for POS interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'pos.html'));
        break;
      case 'admin':
        // Automatically maximize window for admin interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'admin.html'));
        break;
      case 'theater':
        // Automatically maximize window for theater interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'theater.html'));
        break;
      case 'welcome':
        // Reset window to original state when navigating to welcome
        if (mainWindow) {
          // Unmaximize if currently maximized
          if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
          }

          // Reset to original dimensions and center
          mainWindow.setSize(1200, 800);
          mainWindow.center();
        }
        mainWindow.loadFile(path.join(__dirname, 'welcome.html'));
        break;
      case 'login':
        // Reset window to original login state when navigating to login
        if (mainWindow) {
          // Unmaximize if currently maximized
          if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
          }

          // Reset to original dimensions and center
          mainWindow.setSize(1200, 800);
          mainWindow.center();
        }
        mainWindow.loadFile(path.join(__dirname, 'login.html'));
        break;
      default:
        console.log('Unknown page:', page);
    }
  } catch (error) {
    console.error('Navigation error:', error);
  }
});

// IPC handler for navigating to specific page files
ipcMain.handle('navigate-to-page', async (event, pagePath) => {
  try {
    const fullPath = path.join(__dirname, 'pages', pagePath);
    console.log('Navigating to page:', fullPath);
    mainWindow.loadFile(fullPath);
    return { success: true };
  } catch (error) {
    console.error('Page navigation error:', error);
    return { success: false, message: error.message };
  }
});

// IPC handler for navigating to admin panel sections
ipcMain.handle('navigate-to-admin-section', async (event, section) => {
  try {
    const adminPath = path.join(__dirname, 'pages', 'admin.html');
    console.log('Navigating to admin section:', section);

    // Load admin page first
    await mainWindow.loadFile(adminPath);

    // Wait a bit for the page to load, then trigger the section
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        if (typeof handleNavClick === 'function') {
          handleNavClick('${section}');
        }
      `);
    }, 500);

    return { success: true };
  } catch (error) {
    console.error('Admin section navigation error:', error);
    return { success: false, message: error.message };
  }
});

// Handle window controls
ipcMain.handle('close-app', () => {
  app.quit();
});

ipcMain.handle('minimize-app', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('toggle-fullscreen', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
      return false;
    } else {
      mainWindow.maximize();
      return true;
    }
  }
  return false;
});

ipcMain.handle('is-fullscreen', () => {
  if (mainWindow) {
    return mainWindow.isMaximized();
  }
  return false;
});

// User Management IPC handlers
ipcMain.handle('create-user', async (event, userData) => {
  try {
    const result = await db.createUser(userData);
    return { success: true, user: result };
  } catch (error) {
    console.error('Error creating user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-users', async () => {
  try {
    const users = await db.getAllUsers();
    return { success: true, users };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-user-by-id', async (event, id) => {
  try {
    const user = await db.getUserById(id);
    return { success: true, user };
  } catch (error) {
    console.error('Error fetching user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-user', async (event, id, userData) => {
  try {
    const result = await db.updateUser(id, userData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-user-password', async (event, id, newPassword) => {
  try {
    const result = await db.updateUserPassword(id, newPassword);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating password:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-user', async (event, id) => {
  try {
    const result = await db.deleteUser(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting user:', error);
    return { success: false, message: error.message };
  }
});

// Permission Management IPC handlers
ipcMain.handle('create-user-permissions', async (event, userId, permissions) => {
  try {
    const result = await db.createUserPermissions(userId, permissions);
    return { success: true, result };
  } catch (error) {
    console.error('Error creating user permissions:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-user-permissions', async (event, userId) => {
  try {
    const permissions = await db.getUserPermissions(userId);
    return { success: true, permissions };
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('force-create-permissions-table', async () => {
  try {
    await db.forceCreatePermissionsTable();
    return { success: true };
  } catch (error) {
    console.error('Error force creating permissions table:', error);
    return { success: false, message: error.message };
  }
});

// Debug handler to check database directly
ipcMain.handle('debug-user-permissions', async (event, username) => {
  try {
    // Get user by username
    const user = await db.getUserByUsername(username);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    // Get permissions
    const permissions = await db.getUserPermissions(user.id);

    return {
      success: true,
      user: user,
      permissions: permissions
    };
  } catch (error) {
    console.error('Error debugging user permissions:', error);
    return { success: false, message: error.message };
  }
});

// Product Management IPC handlers
ipcMain.handle('create-product', async (event, productData, locationStocks) => {
  try {
    const result = await db.createProduct(productData);

    // Create location stocks if provided
    if (locationStocks && locationStocks.length > 0) {
      await db.createLocationStocks(result.id, locationStocks);
    }

    return { success: true, product: result };
  } catch (error) {
    console.error('Error creating product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-products', async () => {
  try {
    console.log('IPC - get-all-products called');

    // Check if user is logged in
    if (!currentUser) {
      console.error('No current user for product access');
      return { success: false, message: 'User not authenticated' };
    }

    let products;
    // Admin users can see all products, others see only their location's products
    if (currentUser.role === 'Admin') {
      products = await db.getAllProducts();
      console.log(`IPC - Admin retrieved ${products.length} products from database`);
    } else {
      if (!currentUser.location_id) {
        console.error('No location assigned for non-admin user');
        return { success: false, message: 'No location assigned' };
      }
      products = await db.getProductsByLocation(currentUser.location_id);
      console.log(`IPC - Retrieved ${products.length} products for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);
    }

    return { success: true, products };
  } catch (error) {
    console.error('IPC - Error fetching products:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-product-by-id', async (event, id) => {
  try {
    const product = await db.getProductById(id);
    let locationStocks = [];

    if (product) {
      locationStocks = await db.getLocationStocksByProductId(id);
    }

    return { success: true, product, locationStocks };
  } catch (error) {
    console.error('Error fetching product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-product-by-barcode', async (event, barcode) => {
  try {
    const product = await db.getProductByBarcode(barcode);
    let locationStocks = [];

    if (product) {
      locationStocks = await db.getLocationStocksByProductId(product.id);
    }

    return { success: true, product, locationStocks };
  } catch (error) {
    console.error('Error fetching product by barcode:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-product', async (event, id, productData, locationStocks) => {
  try {
    const result = await db.updateProduct(id, productData);

    // Update location stocks if provided
    if (locationStocks && locationStocks.length > 0) {
      await db.updateLocationStocks(id, locationStocks);
    }

    return { success: true, result };
  } catch (error) {
    console.error('Error updating product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-product', async (event, id) => {
  try {
    const result = await db.deleteProduct(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting product:', error);
    return { success: false, message: error.message };
  }
});

// Image upload handler for products
ipcMain.handle('save-product-image', async (event, fileData, barcode) => {
  try {
    // Create images directory if it doesn't exist
    const imagesDir = path.join(__dirname, 'images', 'products');
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
    }

    // Generate unique filename using barcode and timestamp
    const timestamp = Date.now();
    const fileExtension = path.extname(fileData.name);
    const fileName = `${barcode}_${timestamp}${fileExtension}`;
    const imagePath = path.join(imagesDir, fileName);

    // Convert base64 data to buffer and save
    const base64Data = fileData.data.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    fs.writeFileSync(imagePath, buffer);

    console.log('Image saved successfully:', imagePath);
    return { success: true, imagePath };
  } catch (error) {
    console.error('Error saving product image:', error);
    return { success: false, message: error.message };
  }
});

// Category Management IPC handlers
ipcMain.handle('create-category', async (event, categoryData) => {
  try {
    const result = await db.createCategory(categoryData);
    return { success: true, category: result };
  } catch (error) {
    console.error('Error creating category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-categories', async () => {
  try {
    const categories = await db.getAllCategories();
    return { success: true, categories };
  } catch (error) {
    console.error('Error fetching categories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-category-by-id', async (event, id) => {
  try {
    const category = await db.getCategoryById(id);
    return { success: true, category };
  } catch (error) {
    console.error('Error fetching category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-category-by-code', async (event, code) => {
  try {
    const category = await db.getCategoryByCode(code);
    return { success: true, category };
  } catch (error) {
    console.error('Error fetching category by code:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-category', async (event, id, categoryData) => {
  try {
    const result = await db.updateCategory(id, categoryData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-category', async (event, id) => {
  try {
    const result = await db.deleteCategory(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-parent-categories', async () => {
  try {
    const categories = await db.getParentCategories();
    return { success: true, categories };
  } catch (error) {
    console.error('Error fetching parent categories:', error);
    return { success: false, message: error.message };
  }
});

// Supplier Management IPC handlers
ipcMain.handle('create-supplier', async (event, supplierData) => {
  try {
    const result = await db.createSupplier(supplierData);
    return { success: true, supplier: result };
  } catch (error) {
    console.error('Error creating supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-suppliers', async () => {
  try {
    const suppliers = await db.getAllSuppliers();
    return { success: true, suppliers };
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-supplier-by-id', async (event, id) => {
  try {
    const supplier = await db.getSupplierById(id);
    return { success: true, supplier };
  } catch (error) {
    console.error('Error fetching supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-supplier-by-name', async (event, name) => {
  try {
    const supplier = await db.getSupplierByName(name);
    return { success: true, supplier };
  } catch (error) {
    console.error('Error fetching supplier by name:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-supplier', async (event, id, supplierData) => {
  try {
    const result = await db.updateSupplier(id, supplierData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-supplier', async (event, id) => {
  try {
    const result = await db.deleteSupplier(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting supplier:', error);
    return { success: false, message: error.message };
  }
});

// Location Management IPC handlers
ipcMain.handle('create-location', async (event, locationData) => {
  try {
    const result = await db.createLocation(locationData);
    return { success: true, location: result };
  } catch (error) {
    console.error('Error creating location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-locations', async () => {
  try {
    const locations = await db.getAllLocations();
    return { success: true, locations };
  } catch (error) {
    console.error('Error fetching locations:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-location-by-id', async (event, id) => {
  try {
    const location = await db.getLocationById(id);
    return { success: true, location };
  } catch (error) {
    console.error('Error fetching location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-location-by-code', async (event, location_code) => {
  try {
    const location = await db.getLocationByCode(location_code);
    return { success: true, location };
  } catch (error) {
    console.error('Error fetching location by code:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-location', async (event, id, locationData) => {
  try {
    const result = await db.updateLocation(id, locationData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-location', async (event, id) => {
  try {
    const result = await db.deleteLocation(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting location:', error);
    return { success: false, message: error.message };
  }
});

// POS System IPC handlers for categories and products
ipcMain.handle('get-pos-categories', async () => {
  try {
    const categories = await db.getAllCategories();
    // Return only parent categories for main category filter
    const parentCategories = categories.filter(cat => !cat.parent_id && cat.status === 'active');
    return { success: true, categories: parentCategories };
  } catch (error) {
    console.error('Error fetching POS categories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-pos-subcategories', async (event, parentCategoryId) => {
  try {
    const categories = await db.getAllCategories();
    // Return subcategories for the selected parent category
    const subcategories = categories.filter(cat =>
      cat.parent_id === parentCategoryId && cat.status === 'active'
    );
    return { success: true, subcategories };
  } catch (error) {
    console.error('Error fetching POS subcategories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-pos-products', async () => {
  try {
    // Validate user location access
    validateUserLocation();
    logSecurityEvent('POS_PRODUCTS_ACCESS', {
      requestType: 'get-pos-products',
      locationId: currentUser.location_id
    });

    // Use location-filtered products
    const products = await db.getProductsByLocation(currentUser.location_id);
    // Get categories and suppliers to populate product details
    const categories = await db.getAllCategories();
    const suppliers = await db.getAllSuppliers();

    // Map products with category and supplier names
    const enrichedProducts = products.map(product => {
      const category = categories.find(c => c.name === product.category);
      const supplier = suppliers.find(s => s.name === product.supplier);

      // Use purchase_price from products table as requested
      const price = parseFloat(product.purchase_price) || 0;

      return {
        id: product.id.toString(),
        name: product.description,
        price: price, // Use purchase_price from products table
        category: product.category,
        subcategory: product.subcategory,
        supplier: product.supplier,
        barcode: product.barcode,
        stock: product.stock || 0,
        daily_item: product.daily_item || 0, // Include daily_item field for deli classification
        image: product.image_path || `https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=${encodeURIComponent(product.description.substring(0, 10))}`
      };
    });

    console.log(`Loaded ${enrichedProducts.length} products for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);

    // Debug pricing for first few products
    if (enrichedProducts.length > 0) {
      console.log('🔍 PRICING DEBUG - First 3 products (using purchase_price from products table):');
      enrichedProducts.slice(0, 3).forEach(product => {
        const originalProduct = products.find(p => p.id.toString() === product.id);
        console.log(`  Product: ${product.name}`);
        console.log(`    Purchase Price (from products table): $${originalProduct?.purchase_price || 'N/A'}`);
        console.log(`    Final Price Used: $${product.price}`);
        console.log(`    Stock: ${product.stock}`);
      });
    }

    return { success: true, products: enrichedProducts };
  } catch (error) {
    console.error('Error fetching POS products:', error);
    return { success: false, message: error.message };
  }
});

// Ticket Management IPC handlers
ipcMain.handle('create-ticket', async (event, ticketData) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const result = await db.createTicket(ticketData);
    return { success: true, ticket: result };
  } catch (error) {
    console.error('Error creating ticket:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-tickets', async () => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized for get-all-tickets');
      return { success: false, message: 'Database not initialized' };
    }

    // Validate user location access
    validateUserLocation();
    logSecurityEvent('TICKETS_ACCESS', {
      requestType: 'get-all-tickets',
      locationId: currentUser.location_id
    });

    // Use location-filtered tickets
    const tickets = await db.getTicketsByLocation(currentUser.location_id);
    console.log(`Loaded ${tickets.length} tickets for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);
    return { success: true, tickets };
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-ticket-by-id', async (event, ticketId) => {
  try {
    const ticket = await db.getTicketById(ticketId);
    return { success: true, ticket };
  } catch (error) {
    console.error('Error fetching ticket:', error);
    return { success: false, message: error.message };
  }
});

// Save ticket photo to file system
ipcMain.handle('save-ticket-photo', async (event, photoData, ticketId) => {
  try {
    if (!photoData) {
      return { success: true, photoPath: null };
    }

    // Create tickets directory if it doesn't exist
    const ticketsDir = path.join(__dirname, 'images', 'tickets');
    if (!fs.existsSync(ticketsDir)) {
      fs.mkdirSync(ticketsDir, { recursive: true });
    }

    // Generate filename with local timestamp using Day.js
    const timestamp = dayjs().format('YYYY-MM-DDTHH-mm-ss-SSS');
    const filename = `ticket_${ticketId}_${timestamp}.jpg`;
    const filePath = path.join(ticketsDir, filename);

    // Convert base64 to buffer and save
    const base64Data = photoData.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');

    fs.writeFileSync(filePath, buffer);

    // Return relative path for database storage
    const relativePath = path.join('images', 'tickets', filename);

    console.log('Ticket photo saved:', relativePath);
    return { success: true, photoPath: relativePath };
  } catch (error) {
    console.error('Error saving ticket photo:', error);
    return { success: false, message: error.message };
  }
});

// Get full path for ticket photo
ipcMain.handle('get-ticket-photo-path', async (event, relativePath) => {
  try {
    if (!relativePath) {
      return { success: false, message: 'No photo path provided' };
    }

    // Convert relative path to absolute path
    const fullPath = path.join(__dirname, relativePath);

    // Check if file exists
    if (fs.existsSync(fullPath)) {
      return { success: true, fullPath };
    } else {
      console.warn('Ticket photo not found:', fullPath);
      return { success: false, message: 'Photo file not found' };
    }
  } catch (error) {
    console.error('Error getting ticket photo path:', error);
    return { success: false, message: error.message };
  }
});

// Banned Ticket Management IPC handlers
ipcMain.handle('ban-ticket', async (event, ticketId, banData) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const result = await db.banTicket(ticketId, banData);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error banning ticket:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-banned-tickets', async (event) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Check if user is logged in and has location
    if (!currentUser || !currentUser.location_id) {
      console.error('No current user or location for banned ticket access');
      return { success: false, message: 'User not authenticated or no location assigned' };
    }

    // Use location-filtered banned tickets
    const bannedTickets = await db.getBannedTicketsByLocation(currentUser.location_id);
    console.log(`Loaded ${bannedTickets.length} banned tickets for location ${currentUser.location_name} (ID: ${currentUser.location_id})`);
    return { success: true, data: bannedTickets };
  } catch (error) {
    console.error('Error getting banned tickets:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('refund-banned-ticket', async (event, bannedTicketId, refundData) => {
  try {
    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const result = await db.refundBannedTicket(bannedTicketId, refundData);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error refunding banned ticket:', error);
    return { success: false, message: error.message };
  }
});

// Draft Sales Management IPC Handlers
ipcMain.handle('create-draft-sale', async (event, draftData) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Generate unique draft sale ID
    const draftSaleId = `DRAFT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Add current user and location info to draft data
    const completeDraftData = {
      ...draftData,
      draftSaleId,
      userId: currentUser.id,
      locationId: currentUser.location_id,
      operatorName: currentUser.name || currentUser.username,
      locationName: currentUser.location_name
    };

    console.log(`Creating draft sale ${draftSaleId} for location ${currentUser.location_name} by ${currentUser.username}`);

    const result = await db.createDraftSale(completeDraftData);
    console.log(`✅ SECURITY: Draft sale ${draftSaleId} saved to location ${currentUser.location_name} only`);

    return { success: true, data: result };
  } catch (error) {
    console.error('Error creating draft sale:', error);
    return { success: false, message: error.message };
  }
});

// Get draft sales by location (location-based access control)
ipcMain.handle('get-draft-sales', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    let drafts;
    if (currentUser.role === 'Admin') {
      // Admin can see all draft sales
      drafts = await db.getAllDraftSales();
      console.log(`Admin retrieved ${drafts.length} draft sales from all locations`);
    } else {
      // Non-admin users can only see draft sales from their location
      if (!currentUser.location_id) {
        console.error('No location assigned for non-admin user');
        return { success: false, message: 'No location assigned to user' };
      }

      drafts = await db.getDraftSalesByLocation(currentUser.location_id);
      console.log(`✅ SECURITY: User retrieved ${drafts.length} draft sales from location ${currentUser.location_name} only`);
    }

    return { success: true, drafts };
  } catch (error) {
    console.error('Error fetching draft sales:', error);
    return { success: false, message: error.message };
  }
});

// Get draft sale details
ipcMain.handle('get-draft-sale-details', async (event, draftSaleId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const draftDetails = await db.getDraftSaleDetails(draftSaleId);

    if (!draftDetails) {
      return { success: false, message: 'Draft sale not found' };
    }

    // Security check: Non-admin users can only access draft sales from their location
    if (currentUser.role !== 'Admin' && draftDetails.location_id !== currentUser.location_id) {
      console.log(`✅ SECURITY: Access denied - User ${currentUser.username} tried to access draft sale from different location`);
      return { success: false, message: 'Access denied: Draft sale not from your location' };
    }

    return { success: true, draft: draftDetails };
  } catch (error) {
    console.error('Error fetching draft sale details:', error);
    return { success: false, message: error.message };
  }
});

// Delete draft sale
ipcMain.handle('delete-draft-sale', async (event, draftSaleId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // First get draft details to check location access
    const draftDetails = await db.getDraftSaleDetails(draftSaleId);

    if (!draftDetails) {
      return { success: false, message: 'Draft sale not found' };
    }

    // Security check: Non-admin users can only delete draft sales from their location
    if (currentUser.role !== 'Admin' && draftDetails.location_id !== currentUser.location_id) {
      console.log(`✅ SECURITY: Delete denied - User ${currentUser.username} tried to delete draft sale from different location`);
      return { success: false, message: 'Access denied: Cannot delete draft sale from different location' };
    }

    const result = await db.deleteDraftSale(draftSaleId);
    console.log(`✅ SECURITY: Draft sale ${draftSaleId} deleted by ${currentUser.username} from location ${currentUser.location_name}`);

    return { success: true, data: result };
  } catch (error) {
    console.error('Error deleting draft sale:', error);
    return { success: false, message: error.message };
  }
});

// Sales Management IPC Handlers
ipcMain.handle('create-sale', async (event, saleData) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Generate unique sale ID
    const saleId = `SALE-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Add current user and location info to sale data
    const completeSaleData = {
      ...saleData,
      saleId,
      userId: currentUser.id,
      locationId: currentUser.location_id,
      operatorName: currentUser.name || currentUser.username,
      locationName: currentUser.location_name
    };

    console.log(`Creating sale ${saleId} for location ${currentUser.location_name} by ${currentUser.username}`);

    const result = await db.createSale(completeSaleData);
    console.log(`✅ SECURITY: Sale ${saleId} saved to location ${currentUser.location_name} only`);

    return { success: true, data: result };
  } catch (error) {
    console.error('Error creating sale:', error);
    return { success: false, message: error.message };
  }
});

// Get sales by location (location-based access control)
ipcMain.handle('get-sales', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    let sales;
    if (currentUser.role === 'Admin') {
      // Admin can see all sales
      sales = await db.getAllSales();
      console.log(`Admin retrieved ${sales.length} sales from all locations`);
    } else {
      // Non-admin users can only see sales from their location
      if (!currentUser.location_id) {
        console.error('No location assigned for non-admin user');
        return { success: false, message: 'No location assigned to user' };
      }

      sales = await db.getSalesByLocation(currentUser.location_id);
      console.log(`✅ SECURITY: User retrieved ${sales.length} sales from location ${currentUser.location_name} only`);
    }

    return { success: true, sales };
  } catch (error) {
    console.error('Error fetching sales:', error);
    return { success: false, message: error.message };
  }
});

// Daily Sales Report IPC Handlers
ipcMain.handle('get-daily-sales-report', async (event, { dateFrom, dateTo, shift, saleType }) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Validate user location access
    validateUserLocation();

    const locationId = currentUser.location_id;
    let reportData = [];

    console.log(`Generating daily sales report for location ${currentUser.location_name} (${locationId})`);
    console.log(`Date range: ${dateFrom} to ${dateTo}, Shift: ${shift}, Type: ${saleType}`);

    // Get data based on sale type filter
    if (saleType === 'sale' || saleType === 'all') {
      const salesData = await db.getDailySalesReport(locationId, dateFrom, dateTo, shift);
      reportData = reportData.concat(salesData);
    }

    if (saleType === 'theater' || saleType === 'all') {
      const theaterData = await db.getTheaterSalesReport(locationId, dateFrom, dateTo, shift);
      reportData = reportData.concat(theaterData);
    }

    if (saleType === 'deli' || saleType === 'all') {
      const deliData = await db.getDeliSalesReport(locationId, dateFrom, dateTo, shift);
      reportData = reportData.concat(deliData);
    }

    console.log(`✅ SECURITY: Daily sales report generated for location ${currentUser.location_name} only`);
    console.log(`Report contains ${reportData.length} records`);

    return {
      success: true,
      reportData,
      location: currentUser.location_name,
      operator: currentUser.name || currentUser.username
    };
  } catch (error) {
    console.error('Error generating daily sales report:', error);
    return { success: false, message: error.message };
  }
});

// Get sale details
ipcMain.handle('get-sale-details', async (event, saleId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const saleDetails = await db.getSaleDetails(saleId);

    if (!saleDetails) {
      return { success: false, message: 'Sale not found' };
    }

    // Security check: Non-admin users can only access sales from their location
    if (currentUser.role !== 'Admin' && saleDetails.location_id !== currentUser.location_id) {
      console.log(`✅ SECURITY: Access denied - User ${currentUser.username} tried to access sale from different location`);
      return { success: false, message: 'Access denied: Sale not from your location' };
    }

    return { success: true, sale: saleDetails };
  } catch (error) {
    console.error('Error fetching sale details:', error);
    return { success: false, message: error.message };
  }
});

// Shift Management IPC Handlers
ipcMain.handle('start-shift', async (event, shiftData) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    // Generate unique shift ID
    const shiftId = `SHIFT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Add current user and location info to shift data
    const completeShiftData = {
      ...shiftData,
      shift_id: shiftId,
      user_id: currentUser.id,
      location_id: currentUser.location_id,
      operator_name: currentUser.name || currentUser.username,
      location_name: currentUser.location_name
    };



    const result = await db.startShift(completeShiftData);

    // Store current shift in memory for quick access
    currentUser.current_shift = result;

    return { success: true, shift: result };
  } catch (error) {
    console.error('Error starting shift:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-current-shift', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const shift = await db.getCurrentShift(currentUser.id, currentUser.location_id);

    if (shift) {
      // Update current shift in memory
      currentUser.current_shift = shift;

      // Calculate remaining time using day.js (same as tickets)
      const now = dayjs();
      const endTime = dayjs(shift.shift_end_time);

      if (!endTime.isValid()) {
        return { success: false, message: 'Invalid shift end time' };
      }

      const remainingMs = endTime.diff(now);
      const remainingMinutes = Math.max(0, Math.floor(remainingMs / (1000 * 60)));

      // Update remaining time in database if it's different
      if (shift.remaining_time_minutes !== remainingMinutes) {
        await db.updateShiftRemainingTime(shift.shift_id, remainingMinutes);
      }

      return {
        success: true,
        shift: {
          ...shift,
          remaining_minutes: remainingMinutes,
          is_near_end: remainingMinutes <= 30 && remainingMinutes > 0,
          is_expired: remainingMinutes <= 0
        }
      };
    } else {
      return { success: true, shift: null };
    }
  } catch (error) {
    console.error('Error getting current shift:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('end-shift', async (event, shiftSummary) => {
  try {
    if (!currentUser || !currentUser.current_shift) {
      return { success: false, message: 'No active shift found' };
    }

    if (!db || !db.db) {
      console.error('Database not initialized');
      return { success: false, message: 'Database not initialized' };
    }

    const shiftId = currentUser.current_shift.shift_id;

    const result = await db.endShift(shiftId, shiftSummary);

    if (result) {
      // Clear current shift from memory
      currentUser.current_shift = null;
      return { success: true };
    } else {
      return { success: false, message: 'Shift not found or already ended' };
    }
  } catch (error) {
    console.error('Error ending shift:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-shift-time', async (event, remainingMinutes) => {
  try {
    if (!currentUser || !currentUser.current_shift) {
      return { success: false, message: 'No active shift found' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const shiftId = currentUser.current_shift.shift_id;
    const result = await db.updateShiftRemainingTime(shiftId, remainingMinutes);

    return { success: result };
  } catch (error) {
    console.error('Error updating shift time:', error);
    return { success: false, message: error.message };
  }
});

// Get shift totals
ipcMain.handle('get-shift-totals', async (event, shiftId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    // Recalculate totals from sales history to ensure accuracy
    const totals = await db.recalculateShiftTotals(shiftId);
    return { success: true, data: totals };
  } catch (error) {
    console.error('Error getting shift totals:', error);
    return { success: false, message: error.message };
  }
});

// Recalculate all shift totals (admin utility)
ipcMain.handle('recalculate-all-shift-totals', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log('🔄 Starting recalculation of all shift totals...');
    const results = await db.recalculateAllShiftTotals();
    console.log(`✅ Recalculated totals for ${results.length} shifts`);

    return {
      success: true,
      message: `Successfully recalculated totals for ${results.length} shifts`,
      data: results
    };
  } catch (error) {
    console.error('Error recalculating all shift totals:', error);
    return { success: false, message: error.message };
  }
});

// Debug: Get user sales history
ipcMain.handle('debug-user-sales', async (event, userId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const query = `
      SELECT s.*,
             COUNT(si.id) as item_count,
             datetime(s.sale_date, 'localtime') as local_sale_date
      FROM sales s
      LEFT JOIN sales_items si ON s.sale_id = si.sale_id
      WHERE s.user_id = ?
      GROUP BY s.id
      ORDER BY s.sale_date DESC
      LIMIT 20
    `;

    const sales = await new Promise((resolve, reject) => {
      db.db.all(query, [userId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    return { success: true, data: sales };
  } catch (error) {
    console.error('Error getting user sales:', error);
    return { success: false, message: error.message };
  }
});

// Debug: Get user shift history
ipcMain.handle('debug-user-shifts', async (event, userId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const query = `
      SELECT *,
             datetime(shift_start_time, 'localtime') as local_start_time,
             datetime(shift_end_time, 'localtime') as local_end_time,
             datetime(actual_end_time, 'localtime') as local_actual_end_time
      FROM shifts
      WHERE user_id = ?
      ORDER BY shift_start_time DESC
      LIMIT 10
    `;

    const shifts = await new Promise((resolve, reject) => {
      db.db.all(query, [userId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    return { success: true, data: shifts };
  } catch (error) {
    console.error('Error getting user shifts:', error);
    return { success: false, message: error.message };
  }
});

// Debug: Check current active shift for user
ipcMain.handle('debug-current-shift', async (event, userId, locationId) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log(`🔍 DEBUG: Checking current shift for user ${userId}, location ${locationId}`);

    const shift = await db.getCurrentShift(userId, locationId);

    if (shift) {
      console.log(`✅ DEBUG: Found active shift:`, shift);
      return { success: true, data: shift, hasActiveShift: true };
    } else {
      console.log(`❌ DEBUG: No active shift found for user ${userId}, location ${locationId}`);
      return { success: true, data: null, hasActiveShift: false };
    }
  } catch (error) {
    console.error('Error checking current shift:', error);
    return { success: false, message: error.message };
  }
});

// Get current shift report
ipcMain.handle('get-current-shift-report', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log(`📊 Getting current shift report for user ${currentUser.username}`);

    // Get current active shift
    const shift = await db.getCurrentShift(currentUser.id, currentUser.location_id);

    if (!shift) {
      return {
        success: true,
        data: { shift: null, sales: [] },
        message: 'No active shift found'
      };
    }

    // Get sales for this shift
    const salesQuery = `
      SELECT s.*,
             datetime(s.sale_date, 'localtime') as local_sale_date
      FROM sales s
      WHERE s.user_id = ?
        AND s.location_id = ?
        AND s.sale_date >= ?
        AND s.sale_date <= COALESCE(?, datetime('now', 'localtime'))
        AND s.status = 'completed'
      ORDER BY s.sale_date DESC
    `;

    const sales = await new Promise((resolve, reject) => {
      db.db.all(salesQuery, [
        currentUser.id,
        currentUser.location_id,
        shift.shift_start_time,
        shift.actual_end_time || shift.shift_end_time
      ], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`✅ SECURITY: Shift report generated for user ${currentUser.username} at location ${currentUser.location_name}`);
    console.log(`Report contains: Shift ${shift.shift_id}, ${sales.length} sales`);

    return {
      success: true,
      data: {
        shift: shift,
        sales: sales
      }
    };
  } catch (error) {
    console.error('Error getting current shift report:', error);
    return { success: false, message: error.message };
  }
});

// Get location operators for shift filtering
ipcMain.handle('get-location-operators', async () => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    const query = `
      SELECT DISTINCT u.id, u.username, u.full_name
      FROM users u
      INNER JOIN shifts s ON u.id = s.user_id
      WHERE s.location_id = ?
      ORDER BY u.username
    `;

    const operators = await new Promise((resolve, reject) => {
      db.db.all(query, [currentUser.location_id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`✅ SECURITY: Operators list for location ${currentUser.location_name} - ${operators.length} operators`);

    return { success: true, data: operators };
  } catch (error) {
    console.error('Error getting location operators:', error);
    return { success: false, message: error.message };
  }
});

// Get shift sales report with filters
ipcMain.handle('get-shift-sales-report', async (event, filters) => {
  try {
    if (!currentUser) {
      return { success: false, message: 'User not authenticated' };
    }

    if (!db || !db.db) {
      return { success: false, message: 'Database not initialized' };
    }

    console.log(`📊 Getting shift sales report with filters:`, filters);

    let query = `
      SELECT s.*, u.username as operator_name, l.name as location_name,
             datetime(s.shift_start_time, 'localtime') as local_start_time,
             datetime(s.shift_end_time, 'localtime') as local_end_time,
             datetime(s.actual_end_time, 'localtime') as local_actual_end_time
      FROM shifts s
      INNER JOIN users u ON s.user_id = u.id
      INNER JOIN locations l ON s.location_id = l.id
      WHERE s.location_id = ?
    `;

    const queryParams = [currentUser.location_id];

    // Apply filters
    if (filters.reportType === 'current') {
      query += ` AND s.status = 'active'`;
    } else {
      // Historical shifts
      if (filters.dateFrom) {
        query += ` AND DATE(s.shift_start_time) >= ?`;
        queryParams.push(filters.dateFrom);
      }
      if (filters.dateTo) {
        query += ` AND DATE(s.shift_start_time) <= ?`;
        queryParams.push(filters.dateTo);
      }
    }

    // Operator filter
    if (filters.operator === 'current') {
      query += ` AND s.user_id = ?`;
      queryParams.push(currentUser.id);
    } else if (filters.operator !== 'all' && filters.operator !== 'current') {
      query += ` AND s.user_id = ?`;
      queryParams.push(parseInt(filters.operator));
    }

    // Status filter
    if (filters.status !== 'all') {
      query += ` AND s.status = ?`;
      queryParams.push(filters.status);
    }

    query += ` ORDER BY s.shift_start_time DESC LIMIT 50`;

    const shifts = await new Promise((resolve, reject) => {
      db.db.all(query, queryParams, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`✅ SECURITY: Shift sales report for location ${currentUser.location_name} - ${shifts.length} shifts found`);
    console.log(`Filters applied: ${JSON.stringify(filters)}`);

    return {
      success: true,
      data: {
        shifts: shifts,
        filters: filters
      }
    };
  } catch (error) {
    console.error('Error getting shift sales report:', error);
    return { success: false, message: error.message };
  }
});
